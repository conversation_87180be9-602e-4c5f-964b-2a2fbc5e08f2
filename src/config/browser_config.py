"""
Configuraciones predefinidas para diferentes contextos de uso del browser helper.
"""

import os
from typing import List, Optional, Dict, Any
from pathlib import Path

def load_env_file():
    """Carga variables de entorno desde el archivo .env manualmente."""
    try:
        # Intentar importar python-dotenv
        from dotenv import load_dotenv
        load_dotenv()
        return True
    except ImportError:
        # Si no está disponible python-dotenv, cargar manualmente
        env_path = Path(__file__).parent.parent.parent / ".env"
        if env_path.exists():
            with open(env_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    return False

# Cargar variables de entorno al importar el módulo
load_env_file()

try:
    from src.utilities.browser_helper import BrowserHelperConfig
    from src.utilities.captcha_helper import CaptchaConfig
except ImportError:
    # Fallback if there are import issues
    class BrowserHelperConfig:
        def __init__(self, **kwargs):
            for key, value in kwargs.items():
                setattr(self, key, value)
    
    class CaptchaConfig(BrowserHelperConfig):
        pass


class BrowserConfigurations:
    """Clase con configuraciones predefinidas para diferentes escenarios.
    """

    @staticmethod
    def get_test_case_config(**overrides) -> BrowserHelperConfig:
        """Configuración para Test Case/Test Suite - ejecución de casos de prueba específicos."""
        defaults = {
            "headless": True,  # Sin interfaz gráfica para ejecución automatizada
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos
            "use_vision": True,  # Con visión para validación de elementos
            "deterministic_rendering": False,  # Recomendado para evitar detección anti-bot
            "highlight_elements": True,  # Highlighting para debugging de tests
            "viewport_expansion": 500,
            "minimum_wait_page_load_time": 0.5,
            "wait_for_network_idle_page_load_time": 1.0,
            "maximum_wait_page_load_time": 15.0,
            "wait_between_actions": 1.0,  # Velocidad moderada para estabilidad
            "max_steps": 50,  # Suficientes pasos para casos de prueba complejos
            "max_failures": 3,  # Permitir algunos reintentos
            "retry_delay": 5,
            "model_provider": "gemini",
            "temperature": 0.1,
            "generate_gif": True  # GIFs para documentación de tests
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_smoke_config(**overrides) -> BrowserHelperConfig:
        """Configuración para Smoke - pruebas rápidas de funcionalidad básica."""
        defaults = {
            "headless": False,  # Visual para observar smoke tests
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos
            "use_vision": True,
            "deterministic_rendering": False,  # Recomendado para evitar detección anti-bot
            "highlight_elements": True,
            "viewport_expansion": 500,
            "minimum_wait_page_load_time": 0.5,
            "wait_for_network_idle_page_load_time": 1.0,
            "maximum_wait_page_load_time": 12.0,
            "wait_between_actions": 1.0,  # Balance entre velocidad y estabilidad
            "max_steps": 30,
            "max_failures": 3,
            "retry_delay": 3,
            "model_provider": "gemini",
            "temperature": 0.1,
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_exploration_config(**overrides) -> BrowserHelperConfig:
        """Configuración para Exploration - exploración general de aplicaciones web."""
        defaults = {
            "headless": False,  # Visual para observar la exploración
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos
            "use_vision": True,  # Con visión para identificar elementos
            "deterministic_rendering": False,  # Recomendado para evitar detección anti-bot
            "highlight_elements": True,
            "viewport_expansion": 800,
            "minimum_wait_page_load_time": 1.0,
            "wait_for_network_idle_page_load_time": 2.0,
            "maximum_wait_page_load_time": 20.0,
            "wait_between_actions": 2.0,  # Tiempo para observar la exploración
            "max_steps": 75,  # Suficientes pasos para exploración completa
            "max_failures": 4,  # Permitir algunos fallos durante exploración
            "retry_delay": 6,
            "model_provider": "gemini",
            "temperature": 0.2,  # Algo de creatividad para exploración
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    @staticmethod
    def get_exploration_deep_config(**overrides) -> BrowserHelperConfig:
        """Configuración para Exploration Deep - exploración exhaustiva y detallada."""
        defaults = {
            "headless": False,  # Visual para observar exploración profunda
            "user_data_dir": None,  # Usar perfil temporal para evitar conflictos
            "use_vision": True,  # Con visión para análisis detallado
            "deterministic_rendering": False,  # Recomendado para evitar detección anti-bot
            "highlight_elements": True,
            "viewport_expansion": 1000,
            "minimum_wait_page_load_time": 1.5,
            "wait_for_network_idle_page_load_time": 3.0,
            "maximum_wait_page_load_time": 45.0,
            "wait_between_actions": 3.0,  # Muy lento para exploración exhaustiva
            "max_steps": 150,  # Muchos pasos para exploración profunda
            "max_failures": 6,  # Más tolerancia a fallos en exploración profunda
            "retry_delay": 10,
            "model_provider": "gemini",
            "temperature": 0.3,  # Mayor creatividad para exploración profunda
            "generate_gif": True
        }
        defaults.update(overrides)
        return BrowserHelperConfig(**defaults)

    # Configuraciones eliminadas: production, web_interface, api_testing, load_testing, captcha, test_suite
    # Solo mantenemos las 4 configuraciones esenciales para Browser-Use v0.5.0

    @staticmethod
    def get_model_provider_from_env() -> str:
        """Obtiene el proveedor de modelo desde variables de entorno."""
        # Check for browser-use specific provider override first
        browser_use_provider = os.getenv("BROWSER_USE_MODEL_PROVIDER")
        if browser_use_provider:
            return browser_use_provider.lower()

        # Prioritize OpenRouter for browser automation (user preference)
        if os.getenv("OPENROUTER_API_KEY"):
            return "openrouter"
        elif os.getenv("OPENAI_API_KEY"):
            return "openai"
        elif os.getenv("ANTHROPIC_API_KEY"):
            return "anthropic"
        elif os.getenv("GOOGLE_API_KEY"):
            return "gemini"
        else:
            return "gemini"  # Default

class EnvironmentBasedConfig:
    """Configuraciones basadas en variables de entorno."""

    @staticmethod
    def get_config_for_environment(**overrides) -> BrowserHelperConfig:
        """
        Obtiene configuración basada en la variable de entorno BROWSER_TEST_ENV.
        Optimizado para Browser-Use v0.5.0 - 4 configuraciones especializadas.
        """
        env = os.getenv("BROWSER_TEST_ENV", "test_case").lower()
        
        # Mapeo de entornos a configuraciones
        env_config_map = {
            "test_case": BrowserConfigurations.get_test_case_config,
            "smoke": BrowserConfigurations.get_smoke_config,
            "exploration": BrowserConfigurations.get_exploration_config,
            "exploration_deep": BrowserConfigurations.get_exploration_deep_config,
            # Compatibilidad con nombres anteriores
            "ci": BrowserConfigurations.get_test_case_config,
            "dev": BrowserConfigurations.get_exploration_config,
            "development": BrowserConfigurations.get_exploration_config,
            "staging": BrowserConfigurations.get_test_case_config,
            "production": BrowserConfigurations.get_test_case_config,
        }
        
        config_func = env_config_map.get(env, BrowserConfigurations.get_test_case_config)
        return config_func(**overrides)


# Configuraciones de conveniencia
def get_config_by_type(config_type: str, **overrides) -> BrowserHelperConfig:
    """
    Obtiene configuración por tipo.
    Optimizado para Browser-Use v0.5.0 - 4 configuraciones especializadas.

    Args:
        config_type: Tipo de configuración (test_case, smoke, exploration, exploration_deep)
        **overrides: Parámetros para sobrescribir

    Returns:
        BrowserHelperConfig configurado
    """
    config_map = {
        "test_case": BrowserConfigurations.get_test_case_config,
        "smoke": BrowserConfigurations.get_smoke_config,
        "exploration": BrowserConfigurations.get_exploration_config,
        "exploration_deep": BrowserConfigurations.get_exploration_deep_config,
    }

    config_func = config_map.get(config_type, BrowserConfigurations.get_test_case_config)
    return config_func(**overrides)

from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
from src.database.models.browser_configuration import BrowserConfiguration
from src.services.browser_configuration_service import BrowserConfigurationService
from src.config.browser_config import BrowserConfigurations
from src.database.connection import get_database
from motor.motor_asyncio import AsyncIOMotorDatabase

logger = logging.getLogger(__name__)

class DefaultConfigurationService:
    """Service for managing default configurations and seeding predefined configs."""
    
    def __init__(self):
        self.browser_config_service = BrowserConfigurationService()
        self.db: Optional[AsyncIOMotorDatabase] = None
    
    async def get_database(self) -> AsyncIOMotorDatabase:
        """Get database instance."""
        if not self.db:
            self.db = await get_database()
        return self.db
    
    async def seed_predefined_configurations(self) -> Dict[str, Any]:
        """Seed predefined configurations as modifiable configs in MongoDB."""
        try:
            db = await self.get_database()
            seeded_configs = []
            
            # Configuraciones predefinidas que se convertirán en semillas modificables
            predefined_seeds = [
                {
                    "config_type": "test_case",
                    "name": "Test Case / Test Suite (Default)",
                    "description": "Configuración base para ejecución de casos de prueba específicos y suites de testing",
                    "settings": self.browser_config_service._browser_helper_config_to_dict(
                        BrowserConfigurations.get_test_case_config()
                    ),
                    "is_predefined_seed": True,
                    "is_system_default": True,
                    "category": "testing"
                },
                {
                    "config_type": "smoke",
                    "name": "Smoke Testing (Default)",
                    "description": "Configuración base para pruebas rápidas de funcionalidad básica - smoke testing",
                    "settings": self.browser_config_service._browser_helper_config_to_dict(
                        BrowserConfigurations.get_smoke_config()
                    ),
                    "is_predefined_seed": True,
                    "is_system_default": True,
                    "category": "smoke"
                },
                {
                    "config_type": "exploration",
                    "name": "Exploration (Default)",
                    "description": "Configuración base para exploración general de aplicaciones web",
                    "settings": self.browser_config_service._browser_helper_config_to_dict(
                        BrowserConfigurations.get_exploration_config()
                    ),
                    "is_predefined_seed": True,
                    "is_system_default": True,
                    "category": "exploration"
                },
                {
                    "config_type": "exploration_deep",
                    "name": "Exploration Deep (Default)",
                    "description": "Configuración base para exploración exhaustiva y detallada de aplicaciones complejas",
                    "settings": self.browser_config_service._browser_helper_config_to_dict(
                        BrowserConfigurations.get_exploration_deep_config()
                    ),
                    "is_predefined_seed": True,
                    "is_system_default": True,
                    "category": "exploration"
                }
            ]
            
            for seed_config in predefined_seeds:
                # Verificar si ya existe una configuración con este config_type y is_predefined_seed=True
                existing = await BrowserConfiguration.find_one({
                    "config_type": seed_config["config_type"],
                    "is_predefined_seed": True
                })
                
                if not existing:
                    # Crear nueva configuración semilla
                    config_data = {
                        **seed_config,
                        "created_at": datetime.utcnow(),
                        "updated_at": datetime.utcnow(),
                        "is_active": True,
                        "usage_count": 0,
                        "tags": ["default", "predefined", seed_config["category"]],
                        "metadata": {
                            "seeded_version": "1.0",
                            "auto_generated": True,
                            "modifiable": True
                        }
                    }
                    
                    new_config = BrowserConfiguration(**config_data)
                    await new_config.insert()
                    seeded_configs.append({
                        "config_type": seed_config["config_type"],
                        "config_id": str(new_config.id),
                        "action": "created"
                    })
                    logger.info(f"Seeded predefined configuration: {seed_config['config_type']}")
                else:
                    seeded_configs.append({
                        "config_type": seed_config["config_type"],
                        "config_id": str(existing.id),
                        "action": "already_exists"
                    })
                    logger.info(f"Predefined configuration already exists: {seed_config['config_type']}")
            
            return {
                "success": True,
                "seeded_configs": seeded_configs,
                "message": f"Seeded {len([c for c in seeded_configs if c['action'] == 'created'])} new predefined configurations"
            }
            
        except Exception as e:
            logger.error(f"Error seeding predefined configurations: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Failed to seed predefined configurations"
            }
    
    async def get_default_configuration_for_area(self, area: str, project_id: Optional[str] = None) -> Optional[BrowserConfiguration]:
        """Get the default configuration for a specific area (smoke, testing, exploration, etc.)."""
        try:
            # Primero buscar configuración personalizada por defecto para el proyecto
            if project_id:
                custom_default = await BrowserConfiguration.find_one({
                    "project_id": project_id,
                    "category": area,
                    "is_project_default": True,
                    "is_active": True
                })
                if custom_default:
                    return custom_default
            
            # Buscar configuración por defecto del sistema para el área
            system_default = await BrowserConfiguration.find_one({
                "category": area,
                "is_system_default": True,
                "is_active": True
            })
            
            return system_default
            
        except Exception as e:
            logger.error(f"Error getting default configuration for area {area}: {str(e)}")
            return None
    
    async def set_project_default_configuration(self, project_id: str, area: str, config_id: str) -> Dict[str, Any]:
        """Set a configuration as default for a specific area in a project."""
        try:
            # Verificar que la configuración existe
            config = await BrowserConfiguration.get(config_id)
            if not config:
                return {
                    "success": False,
                    "error": "Configuration not found",
                    "message": "La configuración especificada no existe"
                }
            
            # Remover el flag de default de otras configuraciones del mismo proyecto y área
            await BrowserConfiguration.find({
                "project_id": project_id,
                "category": area,
                "is_project_default": True
            }).update({"$set": {"is_project_default": False, "updated_at": datetime.utcnow()}})
            
            # Establecer la nueva configuración como default
            await config.update({"$set": {
                "is_project_default": True,
                "project_id": project_id,
                "category": area,
                "updated_at": datetime.utcnow()
            }})
            
            return {
                "success": True,
                "config_id": config_id,
                "project_id": project_id,
                "area": area,
                "message": f"Configuración establecida como default para {area} en el proyecto"
            }
            
        except Exception as e:
            logger.error(f"Error setting project default configuration: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error estableciendo configuración por defecto"
            }
    
    async def get_area_configurations(self, area: str, project_id: Optional[str] = None) -> List[BrowserConfiguration]:
        """Get all configurations for a specific area."""
        try:
            query = {"category": area, "is_active": True}
            if project_id:
                query["$or"] = [
                    {"project_id": project_id},
                    {"is_predefined_seed": True},
                    {"project_id": {"$exists": False}}
                ]
            
            configs = await BrowserConfiguration.find(query).to_list()
            return configs
            
        except Exception as e:
            logger.error(f"Error getting area configurations: {str(e)}")
            return []
    
    async def initialize_system_defaults(self) -> Dict[str, Any]:
        """Initialize system with default configurations."""
        try:
            # Sembrar configuraciones predefinidas
            seed_result = await self.seed_predefined_configurations()
            
            # Verificar que todas las áreas tienen configuraciones por defecto
            areas = ["testing", "smoke", "exploration"]
            missing_defaults = []
            
            for area in areas:
                default_config = await self.get_default_configuration_for_area(area)
                if not default_config:
                    missing_defaults.append(area)
            
            return {
                "success": True,
                "seed_result": seed_result,
                "missing_defaults": missing_defaults,
                "message": "Sistema inicializado con configuraciones por defecto"
            }
            
        except Exception as e:
            logger.error(f"Error initializing system defaults: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "Error inicializando configuraciones por defecto del sistema"
            }
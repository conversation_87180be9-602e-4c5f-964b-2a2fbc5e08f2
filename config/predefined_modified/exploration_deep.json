{"config_type": "exploration_deep", "name": "Exploration Deep", "description": "Configuración para exploración exhaustiva y detallada de aplicaciones complejas", "settings": {"model_name": "openai/gpt-4.1-mini", "embedder_model": "all-MiniLM-L6-v2", "embedder_provider": "huggingface", "embedder_dims": 384, "model_provider": "openrouter", "temperature": 0.1, "memory_agent_id": "browser_use_agent"}, "execution_types": ["codegen"], "updated_at": "2025-07-09T21:57:04.467713", "is_modified": true, "warnings": []}
{"config_type": "smoke", "name": "Smoke Testing Updated", "description": "Updated smoke testing configuration", "settings": {"headless": true, "use_vision": true, "enable_memory": false, "highlight_elements": true, "deterministic_rendering": false, "disable_security": false, "minimum_wait_page_load_time": 0.5, "wait_for_network_idle_page_load_time": 1.0, "maximum_wait_page_load_time": 10.0, "wait_between_actions": 0.5, "viewport_expansion": 500, "max_steps": 25, "max_failures": 3, "retry_delay": 5.0, "temperature": 0.0, "memory_interval": 10, "generate_gif": false, "keep_alive": false, "model_name": "openai/gpt-4.1-mini", "embedder_model": "all-MiniLM-L6-v2", "embedder_provider": "huggingface", "embedder_dims": 384, "model_provider": "openrouter", "memory_agent_id": "browser_use_agent"}, "updated_at": "2025-07-09T20:55:03.382472", "is_predefined": true, "warnings": []}
# Tech Context: AgentQA - Automatización de Pruebas con IA

## Technologies Used

### **Backend (Python)**
*   **Core Framework:** FastAPI - API REST moderna y asíncrona
*   **Web Server:** Uvicorn - Servidor ASGI de alto rendimiento
*   **Browser Automation:** browser-use==0.5.0 - Automatización web con IA avanzada
    *   DOM Processing System con ClickableElementProcessor
    *   History Tree Processor para tracking de cambios
    *   Multi-LLM support (OpenAI, Anthropic, Google, Groq, Ollama)
    *   MCP (Model Context Protocol) Integration
    *   Observability & Telemetry con lmnr/Posthog
    *   File System Integration avanzado
    *   Token Management y optimización
    *   Gmail Integration especializada
*   **AI/LLM Frameworks:**
    *   LangChain - Framework para aplicaciones con LLM
    *   langchain-google-genai - Integración con Gemini
    *   langchain-openai - Integración con OpenAI
    *   langchain-anthropic - Integración con Claude
    *   langchain-groq - Integración con Groq
    *   agno - Framework adicional de IA

### **Frontend (TypeScript/React)**
*   **Framework:** Next.js 15+ con App Router
*   **UI Framework:** React 18+ con TypeScript
*   **Styling:** Tailwind CSS + shadcn/ui components
*   **State Management:** TanStack Query (React Query)
*   **Form Management:** React Hook Form + Zod validation
*   **UI Components:**
    *   Radix UI primitives (@radix-ui/react-*)
    *   Lucide React icons
    *   Custom shadcn/ui components
*   **Features Implemented:**
    *   Complete dashboard with sidebar navigation
    *   Project management interface
    *   QA Assistant with AI-powered tools
    *   Smoke Test Playground
    *   Browser Configuration Manager
    *   Prompts management with ES↔EN translation

### **Data Processing & Utilities**
*   **Data Science:** NumPy, Pandas
*   **CLI Interface:** Custom CLI con Python
*   **Formatting:** Tabulate para salida de datos

### **Development Tools**
*   **Environment:** Python-dotenv para configuración
*   **API Documentation:** FastAPI auto-genera Swagger/OpenAPI
*   **Type Safety:** Pydantic para validación de datos
*   **Package Management:** npm/yarn para frontend, pip para backend

## Development Setup

### **Backend Requirements**
```bash
# Configurar entorno virtual (recomendado)
python3.11 -m venv .venv
source .venv/bin/activate  # Linux/macOS
# .\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Instalar dependencias Python
pip install -r requirements.txt

# Instalar navegadores de Playwright
playwright install

# Variables de entorno requeridas
GOOGLE_API_KEY=your_gemini_api_key  # Principal
OPENAI_API_KEY=your_openai_key     # Opcional
ANTHROPIC_API_KEY=your_claude_key  # Opcional
GROQ_API_KEY=your_groq_key        # Opcional

# Configuración del servidor
API_HOST=0.0.0.0                  # Por defecto
API_PORT=8000                     # Por defecto
```

### **Frontend Requirements**
```bash
# Navegar al directorio web
cd web/
 
# Instalar dependencias
npm install

# Variables de entorno
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000/api

# Desarrollo
npm run dev  # Puerto 9002 con Turbopack

# Build para producción
npm run build
npm start
```

### **Project Structure**
```
AgentQA/
├── src/                      # Backend Python
│   ├── API/                  # FastAPI routes modulares
│   ├── Agents/              # Agentes de IA
│   ├── Config/              # Configuraciones
│   ├── Core/                # Funcionalidades core
│   ├── Utilities/           # Utilidades y servicios
│   └── Observability/       # Monitoreo y logs
├── web/                     # Frontend Next.js (PRINCIPAL)
│   ├── src/
│   │   ├── app/             # App Router pages
│   │   ├── components/      # React components
│   │   ├── lib/             # Utilities y API clients
│   │   └── types/           # TypeScript definitions
│   ├── public/              # Static assets
│   └── package.json         # Dependencies y scripts
├── prompts/                 # Templates de prompts
├── projects/                # Datos de proyectos
├── tests/                   # Historial de ejecuciones
├── memory-bank/             # Contexto para Copilot
└── browseruse-Docs/         # Documentación browser-use
```

## Technical Constraints

### **AI Model Dependencies**
*   **Primary:** Google Gemini (requires GOOGLE_API_KEY)
*   **Fallbacks:** OpenAI GPT, Anthropic Claude, Groq
*   **Limitation:** Sin API keys, funcionalidad limitada

### **Browser Automation Constraints**
*   **browser-use v0.2.5:** Versión específica para compatibilidad
*   **Headless Mode:** Configurable, pero requerido para producción
*   **Vision Capabilities:** Depende del modelo de IA seleccionado
*   **Network Requirements:** Acceso a internet para ejecución de pruebas

### **Performance Considerations**
*   **Concurrent Execution:** FastAPI soporta operaciones asíncronas
*   **Memory Usage:** Ejecuciones de navegador pueden ser intensivas
*   **API Rate Limits:** Limitado por quotas de proveedores de IA
*   **Storage:** Historial de pruebas incluye screenshots (espacio)

### **Security Constraints**
*   **API Keys:** Manejo seguro de credenciales de IA
*   **CORS:** Configurado para desarrollo local
*   **File System:** Acceso controlado para lectura/escritura de proyectos
*   **Browser Security:** Opción para deshabilitar seguridad en pruebas

### **Platform Compatibility**
*   **Backend:** Cross-platform Python 3.8+
*   **Frontend:** Navegadores modernos con soporte ES2017+
*   **Browser Automation:** Depende de Chromium/Chrome disponible
*   **CLI:** Compatible con shells Unix-like y Windows

### **Development Constraints**
*   **TypeScript:** Configurado con strict mode
*   **ESLint/Build:** Errores ignorados para desarrollo rápido
*   **Hot Reload:** Turbopack habilitado para desarrollo
*   **API Documentation:** Auto-generada, accesible en /docs

### **Internationalization**
*   **Backend:** Inglés para ejecución (browser-use compatibility)
*   **Frontend:** Soporte español/inglés
*   **Translation API:** OpenAI para traducción automática
*   **User Interface:** Idioma configurable por usuario

## Browser-Use Advanced Features

### **DOM Processing System**
*   **DomService:** Servicio principal para manipulación del DOM
    *   Extracción automática de elementos clickeables
    *   Cache de XPath para optimización de rendimiento
    *   Integración con JavaScript para manipulación directa
    *   Procesamiento de árboles DOM complejos

*   **ClickableElementProcessor:** Procesamiento inteligente de elementos
    *   Identificación automática de elementos interactivos
    *   Generación de hashes únicos para identificación
    *   Filtrado avanzado por tipo y propiedades
    *   Optimización de selección de elementos

*   **HistoryTreeProcessor:** Gestión del historial DOM
    *   Comparación de elementos entre estados
    *   Tracking de cambios en estructura DOM
    *   Generación de hashes para identificación única
    *   Conversión entre formatos de elementos

### **Browser Profile System**
*   **Configuración Flexible:** Múltiples tipos de configuración
    *   BrowserLaunchArgs: Argumentos de lanzamiento
    *   BrowserContextArgs: Configuración de contexto
    *   BrowserConnectArgs: Parámetros de conexión
    *   BrowserNewContextArgs: Nuevos contextos

*   **Detección Automática:** Configuración inteligente
    *   Detección automática de tamaño de pantalla
    *   Configuración adaptativa headless/headful
    *   Ajuste automático de viewport y ventana
    *   Soporte para múltiples monitores

*   **Canales Soportados:**
    *   Chromium, Chrome (stable, beta, dev, canary)
    *   Microsoft Edge (stable, beta, dev, canary)
    *   Configuración automática según disponibilidad

### **Multi-LLM Architecture**
*   **Proveedores Soportados:**
    *   OpenAI (GPT-3.5, GPT-4, GPT-4 Turbo)
    *   Anthropic (Claude 3 Sonnet, Haiku, Opus)
    *   Google (Gemini Pro, Gemini Pro Vision)
    *   Groq (Llama, Mixtral)
    *   Ollama (modelos locales)
    *   Azure OpenAI
    *   OpenRouter (acceso a múltiples modelos)

*   **Configuración Avanzada:**
    *   Fallback automático entre proveedores
    *   Configuración específica por tarea
    *   Optimización de costos por modelo
    *   Balanceador de carga inteligente

### **Observability & Telemetry**
*   **Observability Module:** Integración con lmnr (Laminar)
    *   Decoradores para trazado de funciones
    *   Modo debug configurable
    *   Métricas de rendimiento automáticas
    *   Compatibilidad opcional con lmnr

*   **Telemetry Service:** Análisis con Posthog
    *   Telemetría anónima configurable
    *   Análisis de patrones de navegación
    *   Respeto por privacidad del usuario
    *   Deshabilitación via variables de entorno

*   **Sync Service:** Sincronización en la nube
    *   Envío de eventos a Browser Use Cloud
    *   Autenticación automática
    *   Reintentos inteligentes
    *   Sincronización de configuraciones

### **MCP Integration**
*   **Model Context Protocol:** Control avanzado del navegador
    *   Servidor MCP integrado
    *   Exposición de capacidades como herramientas
    *   Control remoto del navegador
    *   Integración con otros sistemas MCP

*   **Herramientas Disponibles:**
    *   Navegación y control de pestañas
    *   Extracción de contenido web
    *   Interacción con elementos DOM
    *   Captura de screenshots y visión
    *   Gestión de sesiones

### **Advanced Integrations**
*   **File System Integration:**
    *   Validación de archivos y extensiones
    *   Gestión de contenido con límites
    *   Soporte para múltiples tipos de archivo
    *   Integración con sistema de memoria

*   **Token Management:**
    *   Estadísticas de uso por modelo
    *   Tracking de costos por proveedor
    *   Límites configurables
    *   Optimización automática de prompts

*   **Gmail Integration:**
    *   Autenticación OAuth2 automática
    *   Lectura y búsqueda de correos
    *   Gestión de etiquetas y carpetas
    *   API especializada para Gmail

### **Configuration System**
*   **Sistema Centralizado:**
    *   Configuración basada en variables de entorno
    *   Migración automática de configuraciones
    *   Perfiles personalizables
    *   Validación automática de parámetros

*   **Directorios de Configuración:**
    *   BROWSER_USE_CONFIG_DIR: Configuración principal
    *   BROWSER_USE_PROFILES_DIR: Perfiles de navegador
    *   Soporte para XDG Base Directory Specification

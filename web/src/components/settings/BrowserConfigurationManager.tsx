"use client";

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Textarea } from "@/components/ui/textarea";
import {
  Toolt<PERSON>,
  TooltipContent,
  Toolt<PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import {
  createCustomConfiguration,
  deleteCustomConfiguration,
  getAllConfigurations,
  getEnvironmentDefaults,
  resolveExecutionTypeConflict,
  testConfiguration,
  updateCustomConfiguration,
  updatePredefinedConfiguration,
  validateExecutionTypes
} from "@/lib/api";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  CheckCircle,
  Copy,
  Edit,
  Eye,
  HelpCircle,
  Info,
  Plus,
  Settings,
  Trash2
} from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { ConfigurationConflictDialog } from "./ConfigurationConflictDialog";

// Execution types
type ExecutionType = "smoke" | "full" | "case" | "suite" | "codegen";

// Configuration form schema
const configurationSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  execution_types: z.array(z.enum(["smoke", "full", "case", "suite", "codegen"])).default(["smoke", "full", "case", "suite", "codegen"]),
  headless: z.boolean().default(true),
  user_data_dir: z.string().nullable().default(null),
  use_vision: z.boolean().default(true),
  enable_memory: z.boolean().default(true),
  highlight_elements: z.boolean().default(false),
  deterministic_rendering: z.boolean().default(true),
  disable_security: z.boolean().default(false),
  minimum_wait_page_load_time: z.number().min(0).default(1),
  wait_for_network_idle_page_load_time: z.number().min(0).default(2),
  maximum_wait_page_load_time: z.number().min(1).default(30),
  wait_between_actions: z.number().min(0).default(1),
  viewport_expansion: z.number().min(0).default(500),
  max_steps: z.number().min(1).default(50),
  max_failures: z.number().min(1).default(3),
  retry_delay: z.number().min(0).default(2),
  temperature: z.number().min(0).max(2).default(0.1),
  model_provider: z.string().default("gemini"),
  memory_interval: z.number().min(1).default(10),
  generate_gif: z.boolean().default(false),
  keep_alive: z.boolean().default(false),
  model_name: z.string().optional(),
  memory_agent_id: z.string().optional(),
  embedder_provider: z.string().optional(),
  embedder_model: z.string().optional(),
  embedder_dims: z.number().min(1).optional(),
});

type ConfigurationFormData = z.infer<typeof configurationSchema>;

interface Configuration {
  id?: string;
  config_id?: string;
  config_type?: string;
  name: string;
  description?: string;
  type?: 'predefined' | 'custom';
  settings: any;
  execution_types?: string[];
  created_at?: string;
  updated_at?: string;
  is_default?: boolean;
}

// Helper component for field labels with tooltips
const FieldLabel = ({
  label,
  tooltip,
  required = false
}: {
  label: string;
  tooltip: string;
  required?: boolean;
}) => (
  <div className="flex items-center gap-2">
    <span className={required ? "after:content-['*'] after:ml-0.5 after:text-red-500" : ""}>
      {label}
    </span>
    <Tooltip>
      <TooltipTrigger asChild>
        <HelpCircle className="h-4 w-4 text-muted-foreground hover:text-foreground cursor-help" />
      </TooltipTrigger>
      <TooltipContent className="max-w-xs">
        <p>{tooltip}</p>
      </TooltipContent>
    </Tooltip>
  </div>
);

// Field help text and tooltips
const FIELD_HELP = {
  // Basic fields
  name: "A unique name to identify this configuration profile",
  description: "Optional description explaining the purpose of this configuration",
  execution_types: "Select which execution types can use this configuration (SMOKE: quick validation, FULL: comprehensive testing, CASE: individual test cases, SUITE: test suite execution, CODEGEN: code generation tasks)",

  // Browser settings
  headless: "Run browser in headless mode (without GUI). Recommended for CI/CD environments",
  use_vision: "Enable vision capabilities for better element detection and interaction",
  enable_memory: "Allow the agent to remember context and previous actions during test execution",
  highlight_elements: "Highlight elements being interacted with (useful for debugging)",
  deterministic_rendering: "Ensure consistent rendering behavior across different environments",
  disable_security: "Disable browser security features (use only in controlled environments)",

  // Timing settings
  minimum_wait_page_load_time: "Minimum time (seconds) to wait after page load before taking actions",
  wait_for_network_idle_page_load_time: "Time (seconds) to wait for network to be idle after page load",
  maximum_wait_page_load_time: "Maximum time (seconds) to wait for page load before timing out",
  wait_between_actions: "Delay (seconds) between consecutive actions to prevent overwhelming the page",
  viewport_expansion: "Pixels to expand viewport size for better element visibility",

  // Execution settings
  max_steps: "Maximum number of steps the agent can take during test execution",
  max_failures: "Maximum number of failures allowed before stopping test execution",
  retry_delay: "Time (seconds) to wait before retrying a failed action",
  temperature: "AI model creativity level (0.0 = deterministic, 2.0 = very creative)",
  model_provider: "AI provider for test execution (gemini, openai, etc.)",
  memory_interval: "Number of steps between memory snapshots",
  generate_gif: "Generate animated GIF of test execution (useful for documentation)",
  keep_alive: "Keep browser session alive between tests for faster execution",

  // Advanced settings
  model_name: "Specific AI model to use (e.g., gemini-2.5-pro, gpt-4, etc.)",
  memory_agent_id: "Identifier for the memory agent managing test context",
  embedder_provider: "Provider for text embeddings (used for semantic understanding)",
  embedder_model: "Specific embedding model for text processing",
  embedder_dims: "Dimensions of the embedding vectors (typically 768 or 1536)",
};

export function BrowserConfigurationManager() {
  const { toast } = useToast();
  const [configurations, setConfigurations] = useState<{
    predefined: Configuration[];
    custom: Configuration[];
  }>({ predefined: [], custom: [] });
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("predefined");
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<Configuration | null>(null);
  const [envDefaults, setEnvDefaults] = useState<any>(null);
  const [executionTypeFilter, setExecutionTypeFilter] = useState<ExecutionType | null>(null);

  // New states for conflict management
  const [isConflictDialogOpen, setIsConflictDialogOpen] = useState(false);
  const [conflictData, setConflictData] = useState<{
    conflicts: any[];
    executionTypes: string[];
    pendingAction: 'create' | 'update' | null;
    pendingData: any;
  }>({
    conflicts: [],
    executionTypes: [],
    pendingAction: null,
    pendingData: null
  });
  const [isResolvingConflict, setIsResolvingConflict] = useState(false);

  const form = useForm<ConfigurationFormData>({
    resolver: zodResolver(configurationSchema),
    defaultValues: {
      name: "",
      description: "",
      execution_types: ["smoke", "full", "case", "suite", "codegen"],
      headless: true,
      use_vision: true,
      enable_memory: true,
      highlight_elements: false,
      deterministic_rendering: true,
      disable_security: false,
      minimum_wait_page_load_time: 1,
      wait_for_network_idle_page_load_time: 2,
      maximum_wait_page_load_time: 30,
      wait_between_actions: 1,
      viewport_expansion: 500,
      max_steps: 50,
      max_failures: 3,
      retry_delay: 2,
      temperature: 0.1,
      model_provider: "gemini",
      memory_interval: 10,
      generate_gif: false,
      keep_alive: false,
    },
  });

  // Load configurations
  const loadConfigurations = async () => {
    setIsLoading(true);
    try {
      const response = await getAllConfigurations();
      setConfigurations({
        predefined: response.predefined || [],
        custom: response.custom || []
      });
    } catch (error) {
      console.error('Failed to load configurations:', error);
      toast({
        title: "Error",
        description: "Failed to load configurations",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Load environment defaults
  const loadEnvironmentDefaults = async () => {
    try {
      const defaults = await getEnvironmentDefaults();
      setEnvDefaults(defaults);
    } catch (error) {
      console.error('Failed to load environment defaults:', error);
      // Don't show error toast for defaults, as they're not critical
    }
  };

  // Validate execution types for conflicts
  const validateExecutionTypesForConflicts = async (
    executionTypes: string[],
    excludeConfigId?: string
  ) => {
    try {
      const response = await validateExecutionTypes({
        execution_types: executionTypes,
        exclude_config_id: excludeConfigId
      });
      return response;
    } catch (error) {
      console.error('Failed to validate execution types:', error);
      toast({
        title: "Validation Error",
        description: "Failed to validate execution types. Please try again.",
        variant: "destructive",
      });
      return { has_conflicts: false, conflicts: [], suggested_action: "proceed" };
    }
  };

  // Handle conflict resolution
  const handleConflictResolution = async (action: 'remove_from_existing' | 'cancel') => {
    if (action === 'cancel') {
      setIsConflictDialogOpen(false);
      setConflictData({
        conflicts: [],
        executionTypes: [],
        pendingAction: null,
        pendingData: null
      });
      return;
    }

    setIsResolvingConflict(true);
    try {
      // Resolve conflicts by removing execution types from existing configurations
      await resolveExecutionTypeConflict({
        action: 'remove_from_existing',
        conflicts: conflictData.conflicts,
        execution_types: conflictData.executionTypes
      });

      // Proceed with the original action
      if (conflictData.pendingAction === 'create') {
        await handleCreateConfiguration(conflictData.pendingData, true); // Skip validation
      } else if (conflictData.pendingAction === 'update') {
        await handleUpdateConfiguration(conflictData.pendingData, true); // Skip validation
      }

      setIsConflictDialogOpen(false);
      setConflictData({
        conflicts: [],
        executionTypes: [],
        pendingAction: null,
        pendingData: null
      });

      toast({
        title: "Conflicts Resolved",
        description: "Execution type conflicts have been resolved successfully.",
      });

    } catch (error) {
      console.error('Failed to resolve conflicts:', error);
      toast({
        title: "Resolution Failed",
        description: "Failed to resolve conflicts. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsResolvingConflict(false);
    }
  };

  useEffect(() => {
    loadConfigurations();
    loadEnvironmentDefaults();
  }, []);

  // Handle opening create modal with environment defaults
  const handleOpenCreateModal = () => {
    // Reset form first
    form.reset();

    // Apply environment defaults if available
    if (envDefaults) {
      const defaultValues: Partial<ConfigurationFormData> = {
        name: "",
        description: "",
        execution_types: ["smoke", "full", "case", "suite", "codegen"],
        headless: true,
        use_vision: true,
        enable_memory: true,
        highlight_elements: false,
        deterministic_rendering: true,
        disable_security: false,
        minimum_wait_page_load_time: 1,
        wait_for_network_idle_page_load_time: 2,
        maximum_wait_page_load_time: 30,
        wait_between_actions: 1,
        viewport_expansion: 500,
        max_steps: 50,
        max_failures: 3,
        retry_delay: 2,
        temperature: 0.1,
        model_provider: "gemini",
        memory_interval: 10,
        generate_gif: false,
        keep_alive: false,
        // Apply environment defaults for advanced fields
        model_name: envDefaults.model_name || "",
        memory_agent_id: envDefaults.memory_agent_id || "",
        embedder_provider: envDefaults.embedder_provider || "",
        embedder_model: envDefaults.embedder_model || "",
        embedder_dims: envDefaults.embedder_dims || undefined,
      };

      form.reset(defaultValues);
    }

    setIsCreateModalOpen(true);
  };

  // Create configuration with conflict validation
  const handleCreate = async (data: ConfigurationFormData, skipValidation = false) => {
    try {
      // Validate execution types for conflicts unless skipping validation
      if (!skipValidation && data.execution_types && data.execution_types.length > 0) {
        const validation = await validateExecutionTypesForConflicts(data.execution_types);

        if (validation.has_conflicts) {
          // Show conflict dialog
          setConflictData({
            conflicts: validation.conflicts,
            executionTypes: data.execution_types,
            pendingAction: 'create',
            pendingData: data
          });
          setIsConflictDialogOpen(true);
          return; // Don't proceed with creation
        }
      }

      await createCustomConfiguration(data);
      toast({
        title: "Success",
        description: "Configuration created successfully",
      });
      setIsCreateModalOpen(false);
      form.reset();
      loadConfigurations();
      setActiveTab("custom");
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create configuration",
        variant: "destructive",
      });
    }
  };

  // Alias for backward compatibility
  const handleCreateConfiguration = handleCreate;

  // Update configuration with conflict validation
  const handleUpdate = async (data: ConfigurationFormData, skipValidation = false) => {
    if (!selectedConfig) return;

    try {
      // Validate execution types for conflicts unless skipping validation
      if (!skipValidation && data.execution_types && data.execution_types.length > 0) {
        const excludeId = selectedConfig.config_id || selectedConfig.config_type;
        const validation = await validateExecutionTypesForConflicts(data.execution_types, excludeId);

        if (validation.has_conflicts) {
          // Show conflict dialog
          setConflictData({
            conflicts: validation.conflicts,
            executionTypes: data.execution_types,
            pendingAction: 'update',
            pendingData: data
          });
          setIsConflictDialogOpen(true);
          return; // Don't proceed with update
        }
      }

      // Update predefined or custom configuration
      if (selectedConfig.type === 'predefined' || selectedConfig.config_type) {
        const configType = selectedConfig.config_type || selectedConfig.config_id;
        await updatePredefinedConfiguration(configType!, data);
      } else {
        await updateCustomConfiguration(selectedConfig.config_id!, data);
      }

      toast({
        title: "Success",
        description: "Configuration updated successfully",
      });
      setIsEditModalOpen(false);
      form.reset();
      setSelectedConfig(null);
      loadConfigurations();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update configuration",
        variant: "destructive",
      });
    }
  };

  // Alias for backward compatibility
  const handleUpdateConfiguration = handleUpdate;

  // Delete configuration
  const handleDelete = async (configId: string) => {
    if (!confirm("Are you sure you want to delete this configuration?")) return;

    try {
      await deleteCustomConfiguration(configId);
      toast({
        title: "Success",
        description: "Configuration deleted successfully",
      });
      loadConfigurations();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete configuration",
        variant: "destructive",
      });
    }
  };

  // Test configuration
  const handleTest = async (config: Configuration) => {
    try {
      await testConfiguration(config.settings);
      toast({
        title: "Success",
        description: "Configuration is valid and ready to use",
      });
    } catch (error) {
      toast({
        title: "Test Failed",
        description: "Configuration validation failed",
        variant: "destructive",
      });
    }
  };

  // Copy configuration as template
  const handleCopy = (config: Configuration) => {
    const formData: ConfigurationFormData = {
      name: `${config.name} (Copy)`,
      description: config.description,
      execution_types: config.execution_types || ["smoke", "full", "case", "suite", "codegen"],
      ...config.settings
    };

    form.reset(formData);
    setIsCreateModalOpen(true);
    toast({
      title: "Template Copied",
      description: "Configuration copied as template",
    });
  };

  // View configuration details
  const handleView = (config: Configuration) => {
    setSelectedConfig(config);
    setIsViewModalOpen(true);
  };

  // Edit configuration
  const handleEdit = (config: Configuration) => {
    setSelectedConfig(config);
    const formData: ConfigurationFormData = {
      name: config.name,
      description: config.description,
      execution_types: config.execution_types || ["smoke", "full", "case", "suite", "codegen"],
      ...config.settings
    };
    form.reset(formData);
    setIsEditModalOpen(true);
  };

  // Filter configurations by execution type
  const filterConfigurationsByExecutionType = (configs: Configuration[]) => {
    if (!executionTypeFilter) return configs;
    return configs.filter(config =>
      config.execution_types?.includes(executionTypeFilter as string)
    );
  };

  const renderConfigurationCard = (config: Configuration, type: 'predefined' | 'custom') => (
    <Card key={config.id || config.config_id || config.name} className="relative">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg">{config.name}</CardTitle>
            <CardDescription className="mt-1">
              {config.description || "No description provided"}
            </CardDescription>
            {config.execution_types && config.execution_types.length > 0 && (
              <div className="flex flex-wrap gap-1 mt-2">
                {config.execution_types.map((execType) => (
                  <Badge key={execType} variant="outline" className="text-xs">
                    {execType.toUpperCase()}
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <Badge variant={type === 'predefined' ? 'default' : 'secondary'}>
            {type}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex gap-2 flex-wrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleView(config)}
          >
            <Eye className="h-4 w-4 mr-1" />
            View
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(config)}
          >
            <Edit className="h-4 w-4 mr-1" />
            Edit
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleCopy(config)}
          >
            <Copy className="h-4 w-4 mr-1" />
            Copy
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleTest(config)}
          >
            <CheckCircle className="h-4 w-4 mr-1" />
            Test
          </Button>

          {type === 'custom' && config.config_id && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleDelete(config.config_id!)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );

  const renderConfigurationForm = () => (
    <Form {...form}>
      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="basic">Basic</TabsTrigger>
          <TabsTrigger value="browser">Browser</TabsTrigger>
          <TabsTrigger value="timing">Timing</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4 mt-4">
          <div className="bg-gray-50 dark:bg-gray-900/50 p-3 rounded-lg border">
            <h4 className="font-medium text-sm mb-1">Basic Configuration</h4>
            <p className="text-xs text-muted-foreground">Essential settings for naming and basic AI model configuration.</p>
          </div>

          <FormField control={form.control} name="name" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Name"
                  tooltip={FIELD_HELP.name}
                  required
                />
              </FormLabel>
              <FormControl>
                <Input placeholder="My Custom Config" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="description" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Description"
                  tooltip={FIELD_HELP.description}
                />
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Description of this configuration..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="execution_types" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Execution Types"
                  tooltip={FIELD_HELP.execution_types}
                />
              </FormLabel>
              <div className="space-y-2">
                <div className="grid grid-cols-2 gap-2">
                  {["smoke", "full", "case", "suite", "codegen"].map((type) => (
                    <div key={type} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`execution_type_${type}`}
                        checked={field.value?.includes(type) || false}
                        onChange={(e) => {
                          const currentValues = field.value || [];
                          if (e.target.checked) {
                            field.onChange([...currentValues, type]);
                          } else {
                            field.onChange(currentValues.filter((v) => v !== type));
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <label htmlFor={`execution_type_${type}`} className="text-sm font-medium capitalize">
                        {type}
                      </label>
                    </div>
                  ))}
                </div>
                <p className="text-xs text-muted-foreground">
                  Select which execution types can use this configuration
                </p>
              </div>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="model_provider" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Model Provider"
                  tooltip={FIELD_HELP.model_provider}
                />
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="gemini">Gemini</SelectItem>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="anthropic">Anthropic</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="temperature" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Temperature"
                  tooltip={FIELD_HELP.temperature}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  max="2"
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
        </TabsContent>

        <TabsContent value="browser" className="space-y-4 mt-4">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border">
            <h4 className="font-medium text-sm mb-1">Browser Settings</h4>
            <p className="text-xs text-muted-foreground">Configure browser behavior, visual features, and rendering options for optimal test execution.</p>
          </div>

          <FormField control={form.control} name="headless" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Headless Mode"
                    tooltip={FIELD_HELP.headless}
                  />
                </FormLabel>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="use_vision" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Use Vision"
                    tooltip={FIELD_HELP.use_vision}
                  />
                </FormLabel>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="highlight_elements" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Highlight Elements"
                    tooltip={FIELD_HELP.highlight_elements}
                  />
                </FormLabel>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="deterministic_rendering" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Deterministic Rendering"
                    tooltip={FIELD_HELP.deterministic_rendering}
                  />
                </FormLabel>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="viewport_expansion" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Viewport Expansion"
                  tooltip={FIELD_HELP.viewport_expansion}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />
        </TabsContent>

        <TabsContent value="timing" className="space-y-4 mt-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg border">
            <h4 className="font-medium text-sm mb-1">Timing Settings</h4>
            <p className="text-xs text-muted-foreground">Control page load waits, action delays, and timeout values for reliable test execution.</p>
          </div>

          <FormField control={form.control} name="minimum_wait_page_load_time" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Minimum Page Load Wait (seconds)"
                  tooltip={FIELD_HELP.minimum_wait_page_load_time}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="wait_for_network_idle_page_load_time" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Network Idle Wait (seconds)"
                  tooltip={FIELD_HELP.wait_for_network_idle_page_load_time}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="maximum_wait_page_load_time" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Maximum Page Load Wait (seconds)"
                  tooltip={FIELD_HELP.maximum_wait_page_load_time}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 30)}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="wait_between_actions" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Wait Between Actions (seconds)"
                  tooltip={FIELD_HELP.wait_between_actions}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  step="0.1"
                  min="0"
                  {...field}
                  onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                />
              </FormControl>
              <FormDescription>Delay between browser actions</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="retry_delay" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Retry Delay (seconds)"
                  tooltip={FIELD_HELP.retry_delay}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="0"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 2)}
                />
              </FormControl>
              <FormDescription>Delay before retrying failed actions</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
        </TabsContent>

        <TabsContent value="advanced" className="space-y-4 mt-4">
          <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg border">
            <h4 className="font-medium text-sm mb-1">Advanced Settings</h4>
            <p className="text-xs text-muted-foreground">Fine-tune execution limits, memory management, AI models, and embedding configurations for specialized use cases.</p>
          </div>

          <FormField control={form.control} name="max_steps" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Maximum Steps"
                  tooltip={FIELD_HELP.max_steps}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 50)}
                />
              </FormControl>
              <FormDescription>Maximum number of actions to perform</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="max_failures" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Maximum Failures"
                  tooltip={FIELD_HELP.max_failures}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 3)}
                />
              </FormControl>
              <FormDescription>Maximum allowed failures before stopping</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="enable_memory" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Enable Memory"
                    tooltip={FIELD_HELP.enable_memory}
                  />
                </FormLabel>
                <FormDescription>Use conversation memory for context</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="memory_interval" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Memory Interval"
                  tooltip={FIELD_HELP.memory_interval}
                />
              </FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="1"
                  {...field}
                  onChange={(e) => field.onChange(parseInt(e.target.value) || 10)}
                />
              </FormControl>
              <FormDescription>How often to save memory (steps)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />

          <FormField control={form.control} name="generate_gif" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Generate GIF"
                    tooltip={FIELD_HELP.generate_gif}
                  />
                </FormLabel>
                <FormDescription>Create animated GIF of test execution</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="keep_alive" render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">
                  <FieldLabel
                    label="Keep Alive"
                    tooltip={FIELD_HELP.keep_alive}
                  />
                </FormLabel>
                <FormDescription>Keep browser session alive after test</FormDescription>
              </div>
              <FormControl>
                <Switch checked={field.value} onCheckedChange={field.onChange} />
              </FormControl>
            </FormItem>
          )} />

          <FormField control={form.control} name="model_name" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Model Name"
                  tooltip={FIELD_HELP.model_name}
                />
              </FormLabel>
              <FormControl>
                <Input placeholder="e.g. gpt-4o, gemini-2.5-flash, claude-3-5-sonnet" {...field} />
              </FormControl>
              <FormDescription>Specific LLM model to use (optional)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
          <FormField control={form.control} name="memory_agent_id" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Memory Agent ID"
                  tooltip={FIELD_HELP.memory_agent_id}
                />
              </FormLabel>
              <FormControl>
                <Input placeholder="browser_use_agent" {...field} />
              </FormControl>
              <FormDescription>Agent ID for procedural memory (optional)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
          <FormField control={form.control} name="embedder_provider" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Embedder Provider"
                  tooltip={FIELD_HELP.embedder_provider}
                />
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select provider" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="openai">OpenAI</SelectItem>
                  <SelectItem value="gemini">Gemini</SelectItem>
                  <SelectItem value="ollama">Ollama</SelectItem>
                  <SelectItem value="huggingface">HuggingFace</SelectItem>
                </SelectContent>
              </Select>
              <FormDescription>Provider for embeddings (optional)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
          <FormField control={form.control} name="embedder_model" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Embedder Model"
                  tooltip={FIELD_HELP.embedder_model}
                />
              </FormLabel>
              <FormControl>
                <Input placeholder="e.g. text-embedding-004, nomic-embed-text, all-MiniLM-L6-v2" {...field} />
              </FormControl>
              <FormDescription>Model for embeddings (optional)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
          <FormField control={form.control} name="embedder_dims" render={({ field }) => (
            <FormItem>
              <FormLabel>
                <FieldLabel
                  label="Embedder Dimensions"
                  tooltip={FIELD_HELP.embedder_dims}
                />
              </FormLabel>
              <FormControl>
                <Input type="number" min="1" {...field} onChange={e => field.onChange(parseInt(e.target.value) || undefined)} />
              </FormControl>
              <FormDescription>Embedding vector dimensions (optional)</FormDescription>
              <FormMessage />
            </FormItem>
          )} />
        </TabsContent>
      </Tabs>
    </Form>
  );

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium">Browser Test Configurations</h3>
            <p className="text-sm text-muted-foreground">
              Manage predefined and custom browser testing configurations
            </p>
          </div>
          <Button onClick={handleOpenCreateModal}>
            <Plus className="h-4 w-4 mr-2" />
            Create Configuration
          </Button>
        </div>

        {/* Execution Type Filter */}
        <div className="flex items-center gap-4">
          <Label className="text-sm font-medium">Filter by Execution Type:</Label>
          <Select value={executionTypeFilter || "all"} onValueChange={(value) => setExecutionTypeFilter(value === "all" ? null : value as ExecutionType)}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All types" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="smoke">SMOKE</SelectItem>
              <SelectItem value="full">FULL</SelectItem>
              <SelectItem value="case">CASE</SelectItem>
              <SelectItem value="suite">SUITE</SelectItem>
              <SelectItem value="codegen">CODEGEN</SelectItem>
            </SelectContent>
          </Select>
          {executionTypeFilter && (
            <Button variant="outline" size="sm" onClick={() => setExecutionTypeFilter(null)}>
              Clear Filter
            </Button>
          )}
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="predefined">
              Predefined ({filterConfigurationsByExecutionType(configurations.predefined).length})
            </TabsTrigger>
            <TabsTrigger value="custom">
              Custom ({filterConfigurationsByExecutionType(configurations.custom).length})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="predefined" className="space-y-4">
            {isLoading ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[...Array(6)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-8 bg-muted rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filterConfigurationsByExecutionType(configurations.predefined).length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    {executionTypeFilter ? `No Predefined Configurations for ${executionTypeFilter.toUpperCase()}` : "No Predefined Configurations"}
                  </h3>
                  <p className="text-sm text-muted-foreground text-center mb-4">
                    {executionTypeFilter
                      ? `No predefined configurations found for execution type "${executionTypeFilter.toUpperCase()}". Try clearing the filter.`
                      : "No predefined configurations available"
                    }
                  </p>
                  {executionTypeFilter && (
                    <Button variant="outline" onClick={() => setExecutionTypeFilter(null)}>
                      Clear Filter
                    </Button>
                  )}
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filterConfigurationsByExecutionType(configurations.predefined).map((config) =>
                  renderConfigurationCard(config, 'predefined')
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="custom" className="space-y-4">
            {isLoading ? (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {[...Array(3)].map((_, i) => (
                  <Card key={i} className="animate-pulse">
                    <CardHeader>
                      <div className="h-4 bg-muted rounded w-3/4"></div>
                      <div className="h-3 bg-muted rounded w-1/2"></div>
                    </CardHeader>
                    <CardContent>
                      <div className="h-8 bg-muted rounded"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : filterConfigurationsByExecutionType(configurations.custom).length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Settings className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">
                    {executionTypeFilter ? `No Custom Configurations for ${executionTypeFilter.toUpperCase()}` : "No Custom Configurations"}
                  </h3>
                  <p className="text-sm text-muted-foreground text-center mb-4">
                    {executionTypeFilter
                      ? `No custom configurations found for execution type "${executionTypeFilter.toUpperCase()}". Try clearing the filter or create a new configuration.`
                      : "Create your first custom configuration to get started"
                    }
                  </p>
                  <div className="flex gap-2">
                    {executionTypeFilter && (
                      <Button variant="outline" onClick={() => setExecutionTypeFilter(null)}>
                        Clear Filter
                      </Button>
                    )}
                    <Button onClick={handleOpenCreateModal}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Configuration
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {filterConfigurationsByExecutionType(configurations.custom).map((config) =>
                  renderConfigurationCard(config, 'custom')
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Create Configuration Modal */}
        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create New Configuration</DialogTitle>
              <DialogDescription className="space-y-2">
                <p>Create a custom browser testing configuration with your preferred settings.</p>
                <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="flex items-start gap-2">
                    <Info className="h-4 w-4 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                    <div className="text-sm text-blue-800 dark:text-blue-200">
                      <p className="font-medium mb-1">💡 Quick Tips:</p>
                      <ul className="space-y-1 text-xs">
                        <li>• Hover over field labels to see detailed explanations</li>
                        <li>• Environment defaults are automatically applied for advanced fields</li>
                        <li>• Use headless mode for CI/CD environments</li>
                        <li>• Enable memory for complex multi-step test scenarios</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              {renderConfigurationForm()}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={form.handleSubmit((data) => handleCreate(data))}>
                Create Configuration
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Edit Configuration Modal */}
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Edit Configuration</DialogTitle>
              <DialogDescription>
                Modify the settings for this configuration.
              </DialogDescription>
            </DialogHeader>
            <div className="py-4">
              {renderConfigurationForm()}
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
                Cancel
              </Button>
              <Button onClick={form.handleSubmit((data) => handleUpdate(data))}>
                Update Configuration
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* View Configuration Modal */}
        <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
          <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Configuration Details</DialogTitle>
              <DialogDescription>
                View all settings and metadata for this configuration.
              </DialogDescription>
            </DialogHeader>
            {selectedConfig && (
              <div className="py-4 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">Name</Label>
                    <p className="text-sm text-muted-foreground">{selectedConfig.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">Type</Label>
                    <Badge variant={selectedConfig.type === 'predefined' ? 'default' : 'secondary'}>
                      {selectedConfig.type || 'custom'}
                    </Badge>
                  </div>
                  {selectedConfig.description && (
                    <div className="col-span-2">
                      <Label className="text-sm font-medium">Description</Label>
                      <p className="text-sm text-muted-foreground">{selectedConfig.description}</p>
                    </div>
                  )}
                  {selectedConfig.created_at && (
                    <div>
                      <Label className="text-sm font-medium">Created</Label>
                      <p className="text-sm text-muted-foreground">
                        {new Date(selectedConfig.created_at).toLocaleString()}
                      </p>
                    </div>
                  )}
                  {selectedConfig.updated_at && (
                    <div>
                      <Label className="text-sm font-medium">Updated</Label>
                      <p className="text-sm text-muted-foreground">
                        {new Date(selectedConfig.updated_at).toLocaleString()}
                      </p>
                    </div>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium">Settings</Label>
                  <pre className="mt-2 p-4 bg-muted rounded-md text-xs overflow-x-auto">
                    {JSON.stringify(selectedConfig.settings, null, 2)}
                  </pre>
                </div>

                {/* Additional fields for model_name, memory_agent_id, embedder_provider, embedder_model, embedder_dims */}
                {selectedConfig.settings.model_name && (
                  <div>
                    <Label>Model Name</Label>
                    <div className="text-sm">{selectedConfig.settings.model_name}</div>
                  </div>
                )}
                {selectedConfig.settings.memory_agent_id && (
                  <div>
                    <Label>Memory Agent ID</Label>
                    <div className="text-sm">{selectedConfig.settings.memory_agent_id}</div>
                  </div>
                )}
                {selectedConfig.settings.embedder_provider && (
                  <div>
                    <Label>Embedder Provider</Label>
                    <div className="text-sm">{selectedConfig.settings.embedder_provider}</div>
                  </div>
                )}
                {selectedConfig.settings.embedder_model && (
                  <div>
                    <Label>Embedder Model</Label>
                    <div className="text-sm">{selectedConfig.settings.embedder_model}</div>
                  </div>
                )}
                {selectedConfig.settings.embedder_dims && (
                  <div>
                    <Label>Embedder Dimensions</Label>
                    <div className="text-sm">{selectedConfig.settings.embedder_dims}</div>
                  </div>
                )}
              </div>
            )}
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsViewModalOpen(false)}>
                Close
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* Conflict Resolution Dialog - TODO: Import component */}
      {/* <ConfigurationConflictDialog
        open={isConflictDialogOpen}
        onOpenChange={setIsConflictDialogOpen}
        conflicts={conflictData.conflicts}
        executionTypes={conflictData.executionTypes}
        onResolve={handleConflictResolution}
        isLoading={isResolvingConflict}
      /> */}
    </TooltipProvider>
  );
}
